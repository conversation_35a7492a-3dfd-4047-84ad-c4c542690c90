import { KeycloakService } from 'keycloak-angular';
import { keycloakConfig } from './keycloak.config';

export function initializeKeycloak(keycloak: KeycloakService) {
  return async (): Promise<boolean> => {
    try {
      console.log('🔐 [K<PERSON><PERSON><PERSON><PERSON>K INIT] Starting Keycloak initialization...');
      console.log('🔐 [KEYCLOAK INIT] Current URL:', window.location.href);
      console.log('🔐 [KEYCLOAK INIT] Config:', keycloakConfig);

      // Test server connectivity first - commented out for now
      // try {
      //   console.log('🔐 [KEYCLOAK INIT] Testing server connectivity...');
      //   const testUrl = `${keycloakConfig.url}/realms/${keycloakConfig.realm}/.well-known/openid_configuration`;
      //   console.log('🔐 [KEYCLOAK INIT] Testing URL:', testUrl);
      //
      //   const response = await fetch(testUrl);
      //   console.log('🔐 [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> INIT] Server response status:', response.status);
      //
      //   if (!response.ok) {
      //     console.warn('🔐 [K<PERSON><PERSON><PERSON><PERSON><PERSON> INIT] Server not accessible, trying fallback...');
      //     const fallbackConfig = { ...keycloakConfig, url: 'http://localhost:8080/auth' };
      //     const fallbackUrl = `${fallbackConfig.url}/realms/${fallbackConfig.realm}/.well-known/openid_configuration`;
      //     console.log('🔐 [KEYCLOAK INIT] Trying fallback URL:', fallbackUrl);
      //
      //     const fallbackResponse = await fetch(fallbackUrl);
      //     console.log('🔐 [KEYCLOAK INIT] Fallback response status:', fallbackResponse.status);
      //
      //     if (fallbackResponse.ok) {
      //       console.log('🔐 [KEYCLOAK INIT] ✅ Using fallback configuration with /auth path');
      //       keycloakConfig.url = fallbackConfig.url;
      //     } else {
      //       console.error('🔐 [KEYCLOAK INIT] ❌ Both URLs failed');
      //     }
      //   } else {
      //     console.log('🔐 [KEYCLOAK INIT] ✅ Server is accessible');
      //   }
      // } catch (connectError) {
      //   console.warn('🔐 [KEYCLOAK INIT] ⚠️ Connectivity test failed:', connectError);
      // }

      console.log('🔐 [KEYCLOAK INIT] Final config being used:', keycloakConfig);

      // Check for existing tokens before initialization
      const existingToken = localStorage.getItem('kc-token') || sessionStorage.getItem('kc-token');
      const existingRefreshToken = localStorage.getItem('kc-refresh-token') || sessionStorage.getItem('kc-refresh-token');
      const wasAuthenticated = localStorage.getItem('kc-authenticated') || sessionStorage.getItem('kc-authenticated');

      console.log('🔐 [KEYCLOAK INIT] Existing token found:', !!existingToken);
      console.log('🔐 [KEYCLOAK INIT] Existing refresh token found:', !!existingRefreshToken);
      console.log('🔐 [KEYCLOAK INIT] Previous auth state:', wasAuthenticated);

      console.log('🔐 [KEYCLOAK INIT] Calling keycloak.init()...');

      // If we have stored tokens, try to restore them to Keycloak instance
      if (existingToken && existingRefreshToken) {
        console.log('🔐 [KEYCLOAK INIT] Attempting to restore tokens to Keycloak instance...');
        try {
          const keycloakInstance = keycloak.getKeycloakInstance();
          if (keycloakInstance) {
            keycloakInstance.token = existingToken;
            keycloakInstance.refreshToken = existingRefreshToken;
            keycloakInstance.authenticated = true;
            console.log('🔐 [KEYCLOAK INIT] ✅ Tokens restored to Keycloak instance');
          }
        } catch (restoreError) {
          console.error('🔐 [KEYCLOAK INIT] ❌ Error restoring tokens:', restoreError);
        }
      }

      const initOptions = {
                  onLoad: 'check-sso' as const,
                  enableLogging: true,
                  redirectUri: window.location.origin + '/',
                 // pkceMethod: 'S256' as const,
                  flow: 'standard' as const,
                  responseMode: 'query' as const,
                  useNonce: false,
                  silentCheckSsoRedirectUri: window.location.origin + '/assets/silent-check-sso.html',
                  loadUserProfileAtStartUp: false,
                  silentCheckSsoFallback: false,
                  checkLoginIframe: false,
                  checkLoginIframeInterval: 5,
                  messageReceiveTimeout: 10000,
                  // Enable browser state for token persistence
                  enableBrowserState: true,
                  // Enhanced token storage with logging
                  tokenStorage: {
                      set: (key: string, value: string) => {
                        console.log('🔐 [TOKEN-STORAGE] Setting token:', key, value ? `${value.substring(0, 20)}...` : 'null');
                        localStorage.setItem(key, value);
                        // Also store in sessionStorage as backup
                        sessionStorage.setItem(key, value);
                      },
                      get: (key: string) => {
                        let value = localStorage.getItem(key);
                        if (!value) {
                          // Fallback to sessionStorage
                          value = sessionStorage.getItem(key);
                          console.log('🔐 [TOKEN-STORAGE] Token not in localStorage, using sessionStorage for:', key);
                        }
                        console.log('🔐 [TOKEN-STORAGE] Getting token:', key, value ? 'present' : 'null');
                        return value;
                      },
                      remove: (key: string) => {
                        console.log('🔐 [TOKEN-STORAGE] Removing token:', key);
                        localStorage.removeItem(key);
                        sessionStorage.removeItem(key);
                      },
                    }
                };

      console.log('🔐 [KEYCLOAK INIT] Init options:', initOptions);
      debugger;

      let authenticated = false;

      // If we have stored tokens, try to validate them first
      if (existingToken && existingRefreshToken && wasAuthenticated) {
        console.log('🔐 [KEYCLOAK INIT] Attempting to validate stored tokens...');

        try {
          // Parse the token to check expiration locally
          const tokenParts = existingToken.split('.');
          let isTokenValid = false;

          if (tokenParts.length === 3) {
            const payload = JSON.parse(atob(tokenParts[1]));
            const now = Math.floor(Date.now() / 1000);
            const exp = payload.exp;

            console.log('🔐 [KEYCLOAK INIT] Token expiry:', new Date(exp * 1000));
            console.log('🔐 [KEYCLOAK INIT] Current time:', new Date());
            console.log('🔐 [KEYCLOAK INIT] Token expired:', now >= exp);

            // Consider token valid if it expires more than 30 seconds from now
            isTokenValid = exp > (now + 30);
          }

          if (isTokenValid) {
            console.log('🔐 [KEYCLOAK INIT] ✅ Stored token is valid, skipping full initialization');

            // Manually set up the Keycloak instance with stored tokens
            const keycloakInstance = keycloak.getKeycloakInstance();
            if (keycloakInstance) {
              keycloakInstance.token = existingToken;
              keycloakInstance.refreshToken = existingRefreshToken;
              keycloakInstance.authenticated = true;

              // Parse the token to get user info
              try {
                const tokenParts = existingToken.split('.');
                if (tokenParts.length === 3) {
                  const payload = JSON.parse(atob(tokenParts[1]));
                  keycloakInstance.tokenParsed = payload;
                  console.log('🔐 [KEYCLOAK INIT] Token parsed successfully');
                }
              } catch (parseError) {
                console.warn('🔐 [KEYCLOAK INIT] Could not parse token:', parseError);
              }

              authenticated = true;
              console.log('🔐 [KEYCLOAK INIT] ✅ Authentication restored from stored tokens');
            }
          } else {
            console.log('🔐 [KEYCLOAK INIT] Stored token is expired, clearing and proceeding with normal initialization');
            // Clear invalid tokens
            localStorage.removeItem('kc-token');
            localStorage.removeItem('kc-refresh-token');
            localStorage.removeItem('kc-authenticated');
            sessionStorage.removeItem('kc-token');
            sessionStorage.removeItem('kc-refresh-token');
            sessionStorage.removeItem('kc-authenticated');
          }
        } catch (validationError) {
          console.error('🔐 [KEYCLOAK INIT] Token validation failed:', validationError);
        }
      }

      // If we couldn't restore from stored tokens, do normal initialization
      if (!authenticated) {
        console.log('🔐 [KEYCLOAK INIT] Proceeding with normal Keycloak initialization...');

        authenticated = await keycloak.init({
          config: keycloakConfig,
          initOptions: initOptions
        }).catch(error => {
          console.error('🔐 [KEYCLOAK INIT] ❌ Init failed with error:', error);
          throw error;
        });
      }

      

      console.log('🔐 [KEYCL-OAK INIT] ✅ Initialization completed!');
      console.log('🔐 [KEYCLOAK INIT] Authenticated:', authenticated);

      try {
        const token = await keycloak.getToken();
        console.log('🔐 [KEYCLOAK INIT] Token present:', !!token);
        console.log('🔐 [KEYCLOAK INIT] Keycloak logged in:', keycloak.isLoggedIn());

        const keycloakInstance = keycloak.getKeycloakInstance();
        if (keycloakInstance) {
          
          console.log('🔐 [KEYCLOAK INIT] Keycloak instance authenticated:', keycloakInstance.authenticated);
          console.log('🔐 [KEYCLOAK INIT] Token expired:', keycloakInstance.isTokenExpired());
          console.log('🔐 [KEYCLOAK INIT] Token parsed:', !!keycloakInstance.tokenParsed);

          if (token) {
            console.log('🔐 [KEYCLOAK INIT] Token preview:', token.substring(0, 50) + '...');

            // Ensure tokens are persisted after successful authentication
            if (authenticated && keycloakInstance.authenticated) {
              console.log('🔐 [KEYCLOAK INIT] Persisting tokens to storage...');

              // Store main token
              if (keycloakInstance.token) {
                localStorage.setItem('kc-token', keycloakInstance.token);
                sessionStorage.setItem('kc-token', keycloakInstance.token);
                console.log('🔐 [KEYCLOAK INIT] Access token stored');
              }

              // Store refresh token
              if (keycloakInstance.refreshToken) {
                localStorage.setItem('kc-refresh-token', keycloakInstance.refreshToken);
                sessionStorage.setItem('kc-refresh-token', keycloakInstance.refreshToken);
                console.log('🔐 [KEYCLOAK INIT] Refresh token stored');
              }

              // Store ID token if available
              if (keycloakInstance.idToken) {
                localStorage.setItem('kc-id-token', keycloakInstance.idToken);
                sessionStorage.setItem('kc-id-token', keycloakInstance.idToken);
                console.log('🔐 [KEYCLOAK INIT] ID token stored');
              }

              // Store authentication state
              localStorage.setItem('kc-authenticated', 'true');
              sessionStorage.setItem('kc-authenticated', 'true');

              console.log('🔐 [KEYCLOAK INIT] ✅ All tokens persisted successfully');
            }
          }

          // Check for authentication mismatch
          if (token && !authenticated) {
            console.warn('🔐 [KEYCLOAK INIT] ⚠️ WARNING: Token exists but authenticated=false');
            console.warn('🔐 [KEYCLOAK INIT] This may cause authentication loops');
          }
        }
      } catch (tokenError) {
        console.error('🔐 [KEYCLOAK INIT] ❌ Error getting token:', tokenError);
      }

      if (authenticated) {
        console.log('🔐 [KEYCLOAK INIT] ✅ User is authenticated');
        //console.log('🔐 [KEYCLOAK INIT] Username:', keycloak.getUsername());
        console.log('🔐 [KEYCLOAK INIT] User roles:', keycloak.getUserRoles());
      } else {
        console.log('🔐 [KEYCLOAK INIT] ❌ User is not authenticated');

        // Check URL for auth callback parameters
        console.log('🔐 [KEYCLOAK INIT] URL search:', window.location.search);
        console.log('🔐 [KEYCLOAK INIT] URL hash:', window.location.hash);

        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(window.location.hash.substring(1));

        if (hashParams.get('code') || urlParams.get('code')) {
          console.log('🔐 [KEYCLOAK INIT] 🔍 Authorization code found in URL - callback processing may have failed');
        }

        if (hashParams.get('error')) {
          console.log('🔐 [KEYCLOAK INIT] ❌ Error in URL hash:', hashParams.get('error'));
        }
      }

      return true; // Always return true to let the app start
    } catch (error) {
      console.error('🔐 [KEYCLOAK INIT] ❌ Initialization failed:', error);
      console.error('🔐 [KEYCLOAK INIT] Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
        config: keycloakConfig,
        currentUrl: window.location.href
      });
      return true;
    }
  };
}